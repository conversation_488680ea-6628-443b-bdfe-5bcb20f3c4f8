<?php
// 简单测试首页控制器
require_once 'vendor/autoload.php';

try {
    // 测试类是否能正常加载
    $articleRepository = new \app\common\repository\ArticleRepository();
    $categoryRepository = new \app\common\repository\ArticleCategoryRepository();
    $configService = new \app\common\service\PortalConfigService();
    $moduleService = new \app\common\service\PortalModuleService();
    
    echo "所有类加载成功\n";
    
    // 测试方法调用
    $categories = $categoryRepository->findAll(true);
    echo "分类数量: " . count($categories) . "\n";
    
    $modules = $moduleService->getEnabledModules();
    echo "模块数量: " . count($modules) . "\n";
    
    $configs = $configService->getConfigByGroup('site');
    echo "配置数量: " . count($configs) . "\n";
    
    echo "所有测试通过！\n";
    
} catch (\Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
