<?php
/**
 * Created by lj.
 * User: lj
 * Date: 2021/6/30
 * Time: 2:13 下午
 */

namespace orm;


use Doctrine\Common\Annotations\AnnotationReader;
use Doctrine\Common\Annotations\FileCacheReader;
use Doctrine\Common\Annotations\Reader;
use orm\mapping\Column;
use orm\mapping\Id;
use orm\mapping\Table;
use orm\mapping\Transient;
use ReflectionClass;
use think\facade\Config;

class Parser
{

    private static $instance = [];

    public static function clear($key=''){
        if($key){
            $rel = new \ReflectionClass($key);
            unset(self::$instance[$rel->getName()]);
        }else{
            self::$instance = [];
        }
    }

    /**
     * 解析数据
    */
    public static function parse($class):?TableMetaInfo{
        $rel = new \ReflectionClass($class);

        //判断缓存是否存在
        $className = $rel->getName();
        if(isset(self::$instance[$className])){
            $tableMetaInfo = self::$instance[$className];
            return $tableMetaInfo;
        }else{
            $tableMetaInfo = new TableMetaInfo();
            $tableMetaInfo->className = $className;
            $tableMetaInfo->classRef = $rel;
            self::$instance[$className] = $tableMetaInfo;//缓存解析数据

            $tableMetaInfo->name = $rel->getShortName();
        }


        // 修复runtime_path函数调用
        $runtimePath = function_exists('runtime_path') ? runtime_path() : __DIR__ . '/../../runtime/';
        // 修复Config调用
        $appDebug = false;
        try {
            if (class_exists('\\think\\facade\\Config')) {
                $appDebug = Config::get('app_debug');
            }
        } catch (\Exception $e) {
            $appDebug = false;
        }
        $reader = new FileCacheReader(new AnnotationReader(), $runtimePath."annotation", $appDebug);
        if($appDebug){
            $reader->clearLoadedAnnotations();
        }

        $annotation = $reader->getClassAnnotation($rel, Table::class);

        if(!$annotation){
            return null;
        }
        $tableMetaInfo->tableName = $annotation->name ?$annotation->name:self::classNameToSplitName($rel->getShortName());
        self::parseProperty($reader, $rel, $tableMetaInfo);
        return $tableMetaInfo;
    }


    public static function parseProperty(Reader $reader, ReflectionClass $rel, TableMetaInfo &$metInfo){
        $metInfo->colArray = [];
        $metInfo->idColArray = [];

        $properties = $rel->getProperties();
        foreach ($properties as $property){
            $annotation = $reader->getPropertyAnnotation($property, Transient::class);
            if($annotation){
                continue;
            }

            $propertyInfo = new ColMetaInfo();
            $propertyInfo->name =  $property->getName();

            $annotation = $reader->getPropertyAnnotation($property, Id::class);
            if($annotation){
                $propertyInfo->isPK = true;
                $propertyInfo->isAuto = boolval($annotation->auto) === 1?true:false;
            }else{
                $propertyInfo->isPK = false;
            }

            $annotation = $reader->getPropertyAnnotation($property, Column::class);
            if($annotation){
                $propertyInfo->colName =  $annotation->name ?$annotation->name:self::attributeNameToSplitName($propertyInfo->name);
            }else{
                $propertyInfo->colName = self::attributeNameToSplitName($propertyInfo->name);
            }

            $propertyInfo->isPK && $metInfo->idColArray[] = $propertyInfo;
            $metInfo->colArray[] = $propertyInfo;
        }
    }


    /**
     * 驼峰转下划线
    */
    public static function classNameToSplitName($name){
        $newName = '';
        $len = strlen($name);
        for($index = 0; $index < $len; $index++){
            $char = substr($name, $index,1);
            if(ord($char) >= 65 && ord($char) <= 90){
                $newName .= '_'.strtolower($char);
            }else{
                $newName .= $char;
            }

        }
        return $newName;
    }

    /**
     * 下划线转驼峰
     */
    public static function splitNameToClassName($name){
        $newName = '';
        $arr = explode('_', $name);
        foreach ($arr as $value){
            $newName .= strtoupper($value[0]).substr($value,1);
        }
        return $newName;
    }


    public static function attributeNameToSplitName($name){
        $newName = '';
        $len = strlen($name);
        for($index = 0; $index < $len; $index++){
            $char = substr($name, $index,1);
            if(ord($char) >= 65 && ord($char) <= 90){
                $newName .= '_'.strtolower($char);
            }else{
                $newName .= $char;
            }
        }
        return $newName;
    }

    public static function splitNameToAttributeName($name){
        $newName = '';
        $arr = explode('_', $name);
        foreach ($arr as $key =>$value){
            if($key === 0){
                $newName .= $value;
            }else{
                $newName .= strtoupper($value[0]).substr($value,1);
            }
        }
        return $newName;
    }
}