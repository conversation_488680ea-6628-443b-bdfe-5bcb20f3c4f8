<!-- 新闻轮播模块 -->
{if !empty($newsData.banner)}
<div class="layui-carousel" id="newsBanner">
    <div carousel-item>
        {volist name="newsData.banner" id="news"}
        <div class="banner-item" style="background-image: url('{$news.cover_image}');">
            <div class="banner-content">
                <div class="banner-title">
                    <a href="/index/article/{$news.id}" style="color: #fff; text-decoration: none;">
                        {$news.title}
                    </a>
                </div>
                {if !empty($news.summary)}
                <div class="banner-summary">{$news.summary}</div>
                {/if}
            </div>
        </div>
        {/volist}
    </div>
</div>
{else}
<div class="module-header">{$module.module_title}</div>
<div class="module-body">
    <div class="ad-container">
        <i class="layui-icon layui-icon-picture" style="font-size: 48px; color: #ddd;"></i>
        <p>暂无轮播内容</p>
        <p style="font-size: 12px; margin-top: 10px;">请在后台添加带有封面图的新闻文章</p>
    </div>
</div>
{/if}
