<?php

namespace app\index\controller;

use app\common\bean\ArticleCategoryBean;
use app\common\bean\ArticleBean;
use app\common\repository\ArticleCategoryRepository;
use app\common\repository\ArticleRepository;
use app\common\service\ArticleService;
use app\common\service\SiteConfigService;
use app\common\repository\PortalModuleRepository;
use app\Request;
use think\App;
use think\exception\HttpException;
use think\facade\View;

class Index extends Base
{
    protected $articleService;
    protected $articleRepository;
    protected $categoryRepository;
    protected $siteConfigService;
    protected $moduleRepository;
    
    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->setCurrentMenu(1);
        $this->articleService = new ArticleService();
        $this->articleRepository = new ArticleRepository();
        $this->categoryRepository = new ArticleCategoryRepository();
        $this->siteConfigService = new SiteConfigService();
        $this->moduleRepository = new PortalModuleRepository();
    }

    /**
     * 首页
     */
    public function index()
    {
        try {
            // 获取分类列表
            $categories = $this->categoryRepository->findVisible();
            
            // 获取模块配置
            $modules = $this->moduleRepository->findEnabled();
            
            // 获取各类新闻数据
            $newsData = [
                'banner' => $this->articleRepository->getBannerArticles(),
                'hot' => $this->articleRepository->getHotArticles(),
                'latest' => $this->articleRepository->getLatestArticles()
            ];
            
            // 获取网站基础配置
            $basicConfigs = $this->siteConfigService->getSiteConfigs();
            
            // 传递数据到视图
            View::assign([
                'categories' => $categories,
                'modules' => $modules,
                'newsData' => $newsData,
                'basicConfigs' => $basicConfigs
            ]);
            
            return View::fetch();
        } catch (\Exception $e) {
            // 记录错误日志
            trace('首页加载失败: ' . $e->getMessage(), 'error');
            
            // 显示错误信息
            View::assign('error', '页面加载失败，请稍后再试');
            return View::fetch();
        }
    }

    /**
     * 文章详情页
     * 
     * @param int $id 文章ID
     */
    public function article($id)
    {
        try {
            // 获取文章详情
            $article = $this->articleService->getById((int)$id);
            
            if (!$article || $article->status !== ArticleBean::STATUS_PUBLISHED) {
                throw new HttpException(404, '文章不存在');
            }
            
            // 更新阅读量
            $this->articleRepository->incrementViewCount($id);
            
            // 获取相关文章
            $relatedArticles = $this->articleRepository->getRelatedArticles($article->categoryId, $article->id);
            
            // 获取分类列表
            $categories = $this->categoryRepository->findVisible();
            
            // 传递数据到视图
            View::assign([
                'article' => $article,
                'relatedArticles' => $relatedArticles,
                'categories' => $categories
            ]);
            
            return View::fetch();
        } catch (HttpException $e) {
            throw $e;
        } catch (\Exception $e) {
            // 记录错误日志
            trace('文章详情页加载失败: ' . $e->getMessage(), 'error');
            
            // 显示错误信息
            View::assign('error', '文章加载失败，请稍后再试');
            return View::fetch('error/index');
        }
    }

    /**
     * 分类文章列表页
     * 
     * @param int $id 分类ID
     */
    public function category($id)
    {
        try {
            // 获取分类信息
            $category = $this->categoryRepository->findById((int)$id);
            
            if (!$category || !$category->isShow) {
                throw new HttpException(404, '分类不存在');
            }
            
            // 获取分页参数
            $page = (int)$this->request->param('page', 1);
            $limit = 10;
            
            // 获取分类下的文章
            $where = [
                'category_id' => $id,
                'status' => ArticleBean::STATUS_PUBLISHED
            ];
            $result = $this->articleRepository->findPublished($page, $limit, $where);
            
            // 获取分类列表
            $categories = $this->categoryRepository->findVisible();
            
            // 传递数据到视图
            View::assign([
                'category' => $category,
                'articles' => $result['list'],
                'pagination' => [
                    'total' => $result['total'],
                    'current_page' => $result['page'],
                    'last_page' => $result['pages'],
                ],
                'categories' => $categories
            ]);
            
            return View::fetch();
        } catch (HttpException $e) {
            throw $e;
        } catch (\Exception $e) {
            // 记录错误日志
            trace('分类页加载失败: ' . $e->getMessage(), 'error');
            
            // 显示错误信息
            View::assign('error', '分类页加载失败，请稍后再试');
            return View::fetch('error/index');
        }
    }

    /**
     * 搜索页面
     */
    public function search()
    {
        try {
            // 获取搜索关键词
            $keyword = $this->request->param('keyword', '');
            
            // 获取分页参数
            $page = (int)$this->request->param('page', 1);
            $limit = 10;
            
            // 如果关键词为空，重定向到首页
            if (empty($keyword)) {
                return redirect('/');
            }
            
            // 搜索文章
            $result = $this->articleRepository->searchArticles($keyword, $page, $limit);
            
            // 获取分类列表
            $categories = $this->categoryRepository->findVisible();
            
            // 传递数据到视图
            View::assign([
                'keyword' => $keyword,
                'articles' => $result['list'],
                'pagination' => [
                    'total' => $result['total'],
                    'current_page' => $result['page'],
                    'last_page' => $result['pages'],
                ],
                'categories' => $categories
            ]);
            
            return View::fetch();
        } catch (\Exception $e) {
            // 记录错误日志
            trace('搜索页加载失败: ' . $e->getMessage(), 'error');
            
            // 显示错误信息
            View::assign('error', '搜索失败，请稍后再试');
            return View::fetch('error/index');
        }
    }
}
