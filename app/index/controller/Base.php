<?php

namespace app\index\controller;

use app\BaseController;
use app\common\bean\Aconfig;
use app\common\repo\AconfigRepo;
use mybatis\BeanHelper;
use mybatis\SqlManager;
use think\App;
use think\facade\Config;
use think\facade\View;

class Base extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);

        View::assign('token', $this->request->token);
    }
}